apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: policy-btls
  namespace: gateway-conformance-infra
spec:
  targetRefs:
    - group: ""
      kind: Service
      name: tls-backend-2
      sectionName: https
  validation:
    caCertificateRefs:
      - name: backend-tls-checks-certificate
        group: ""
        kind: ConfigMap
    hostname: example.com
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: http-with-backend-tls
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /backend-tls
      backendRefs:
        - name: tls-backend-2
          port: 443
---
apiVersion: v1
kind: Service
metadata:
  name: tls-backend-2-no-policy
  namespace: gateway-conformance-infra
spec:
  selector:
    app: tls-backend-2
  ports:
    - protocol: TCP
      port: 443
      targetPort: 8443
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: http-without-backend-tls
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /backend-tls-without-policy
      backendRefs:
        - name: tls-backend-2-no-policy
          port: 443
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: policy-btls-trust-store
  namespace: gateway-conformance-infra
spec:
  targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-eg-site
  validation:
    wellKnownCACertificates: System
    hostname: gateway.envoyproxy.io
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: http-with-backend-tls-system-trust-store
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  hostnames:
    - gateway.envoyproxy.io
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: backend-eg-site
          group: gateway.envoyproxy.io
          kind: Backend
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-eg-site
  namespace: gateway-conformance-infra
spec:
  endpoints:
    - fqdn:
        hostname: gateway.envoyproxy.io
        port: 443
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: policy-btls-ca-mismatch
  namespace: gateway-conformance-infra
spec:
  targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-insecure-tls-verify
  validation:
    wellKnownCACertificates: System
    hostname: example.com
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-insecure-tls-verify
  namespace: gateway-conformance-infra
spec:
  endpoints:
    - fqdn:
        hostname: tls-backend-2.gateway-conformance-infra.svc.cluster.local
        port: 443
  tls:
    insecureSkipVerify: true
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: http-with-backend-insecure-skip-verify
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /backend-tls-skip-verify
      backendRefs:
        - name: backend-insecure-tls-verify
          group: gateway.envoyproxy.io
          kind: Backend
