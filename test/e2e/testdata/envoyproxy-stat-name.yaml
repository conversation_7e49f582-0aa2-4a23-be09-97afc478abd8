apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: stat-name-gtw
  namespace: gateway-conformance-infra
spec:
  gatewayClassName: "{GATEWAY_CLASS_NAME}"
  listeners:
    - name: http
      port: 80
      protocol: HTTP
      allowedRoutes:
        namespaces:
          from: Same
  infrastructure:
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: stat-name-ep
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: stat-name-ep
  namespace: gateway-conformance-infra
spec:
  ipFamily: IPv4
  telemetry:
    metrics:
      clusterStatName: "%ROUTE_NAMESPACE%/%ROUTE_NAME%/%BACKEND_REFS%"
  shutdown:
    drainTimeout: 5s
    minDrainDuration: 1s
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: stat-name-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: stat-name-gtw
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /foo
      backendRefs:
        - name: infra-backend-v1
          port: 8080
    - matches:
        - path:
            type: PathPrefix
            value: /bar
      backendRefs:
        - name: infra-backend-v1
          port: 8080
