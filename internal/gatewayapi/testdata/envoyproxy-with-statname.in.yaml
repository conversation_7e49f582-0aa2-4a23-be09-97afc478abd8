envoyProxyForGatewayClass:
  apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyProxy
  metadata:
    namespace: envoy-gateway-system
    name: test
  spec:
    telemetry:
      metrics:
        clusterStatName: "%ROUTE_KIND%/%ROUTE_NAMESPACE%/%ROUTE_NAME%/%ROUTE_RULE_NAME%/%ROUTE_RULE_NUMBER%/%BACKEND_REFS%"
    provider:
      type: Kubernetes
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
grpcRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: GRPCRoute
  metadata:
    namespace: default
    name: grpcroute-1
  spec:
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      - name: service-2
        port: 8080
      - name: service-3
        port: 8080
      - name: service-4
        port: 8080
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/foo"
      name: "foo"
      backendRefs:
      - name: service-3
        port: 8080
      - name: service-4
        port: 8080
    - matches:
      - path:
          value: "/"
      name: "fallback"
      backendRefs:
      - name: service-1
        port: 8080
