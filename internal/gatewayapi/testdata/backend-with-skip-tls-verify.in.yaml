gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      name: gateway-btls
      namespace: envoy-gateway
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
httpRoutes:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      name: httproute-btls
      namespace: envoy-gateway
    spec:
      parentRefs:
        - namespace: envoy-gateway
          name: gateway-btls
          sectionName: http
      rules:
        - matches:
            - path:
                type: Exact
                value: "/exact"
          backendRefs:
            - kind: Backend
              group: gateway.envoyproxy.io
              name: backend-1
              namespace: backends
              port: 8080

referenceGrants:
  - apiVersion: gateway.networking.k8s.io/v1alpha2
    kind: ReferenceGrant
    metadata:
      name: refg-route-svc
      namespace: backends
    spec:
      from:
        - group: gateway.networking.k8s.io
          kind: HTTPRoute
          namespace: envoy-gateway
        - group: gateway.networking.k8s.io
          kind: Gateway
          namespace: envoy-gateway
        - group: gateway.networking.k8s.io
          kind: BackendTLSPolicy
          namespace: policies
      to:
        - group: gateway.envoyproxy.io
          kind: Backend

backends:
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: Backend
    metadata:
      name: backend-1
      namespace: backends
    spec:
      endpoints:
        - ip:
            address: *******
            port: 3001
      tls:
        insecureSkipVerify: true

secrets:
  - apiVersion: v1
    kind: Secret
    metadata:
      name: ca-secret
      namespace: policies
    data:
      ca.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKekNDQWcrZ0F3SUJBZ0lVQWw2VUtJdUttenRlODFjbGx6NVBmZE4ySWxJd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0l6RVFNQTRHQTFVRUF3d0hiWGxqYVdWdWRERVBNQTBHQTFVRUNnd0dhM1ZpWldSaU1CNFhEVEl6TVRBdwpNakExTkRFMU4xb1hEVEkwTVRBd01UQTFOREUxTjFvd0l6RVFNQTRHQTFVRUF3d0hiWGxqYVdWdWRERVBNQTBHCkExVUVDZ3dHYTNWaVpXUmlNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXdTVGMKMXlqOEhXNjJueW5rRmJYbzRWWEt2MmpDMFBNN2RQVmt5ODdGd2VaY1RLTG9XUVZQUUUycDJrTERLNk9Fc3ptTQp5eXIreHhXdHlpdmVyZW1yV3FuS2tOVFloTGZZUGhnUWtjemliN2VVYWxtRmpVYmhXZEx2SGFrYkVnQ29kbjNiCmt6NTdtSW5YMlZwaURPS2c0a3lIZml1WFdwaUJxckN4MEtOTHB4bzNERVFjRmNzUVRlVEh6aDQ3NTJHVjA0UlUKVGkvR0VXeXpJc2w0Umc3dEd0QXdtY0lQZ1VOVWZZMlEzOTBGR3FkSDRhaG4rbXcvNmFGYlczMVc2M2Q5WUpWcQppb3lPVmNhTUlwTTVCL2M3UWM4U3VoQ0kxWUdoVXlnNGNSSExFdzVWdGlraW95RTNYMDRrbmEzalFBajU0WWJSCmJwRWhjMzVhcEtMQjIxSE9VUUlEQVFBQm8xTXdVVEFkQmdOVkhRNEVGZ1FVeXZsMFZJNXZKVlN1WUZYdTdCNDgKNlBiTUVBb3dId1lEVlIwakJCZ3dGb0FVeXZsMFZJNXZKVlN1WUZYdTdCNDg2UGJNRUFvd0R3WURWUjBUQVFILwpCQVV3QXdFQi96QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFNTHhyZ0ZWTXVOUnEyd0F3Y0J0N1NuTlI1Q2Z6CjJNdlhxNUVVbXVhd0lVaTlrYVlqd2RWaURSRUdTams3SlcxN3ZsNTc2SGpEa2RmUndpNEUyOFN5ZFJJblpmNkoKaThIWmNaN2NhSDZEeFIzMzVmZ0hWekxpNU5pVGNlL09qTkJRelEyTUpYVkRkOERCbUc1ZnlhdEppT0pRNGJXRQpBN0ZsUDBSZFAzQ08zR1dFME01aVhPQjJtMXFXa0UyZXlPNFVIdndUcU5RTGRyZEFYZ0RRbGJhbTllNEJHM0dnCmQvNnRoQWtXRGJ0L1FOVCtFSkhEQ3ZoRFJLaDFSdUdIeWcrWSsvbmViVFdXckZXc2t0UnJiT29IQ1ppQ3BYSTEKM2VYRTZudDBZa2d0RHhHMjJLcW5ocEFnOWdVU3MyaGxob3h5dmt6eUYwbXU2TmhQbHdBZ25xNysvUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

backendTLSPolicies:
  - apiVersion: gateway.networking.k8s.io/v1alpha2
    kind: BackendTLSPolicy
    metadata:
      name: policy-btls
      namespace: backends
    spec:
      targetRefs:
        - kind: Backend
          group: gateway.envoyproxy.io
          name: backend-1
      validation:
        caCertificateRefs:
          - name: ca-secret
            group: ""
            kind: Secret
        hostname: example.com
