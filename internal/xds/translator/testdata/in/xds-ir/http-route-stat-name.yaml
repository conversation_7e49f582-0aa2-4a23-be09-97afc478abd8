http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "first-route"
    hostname: "*"
    destination:
      statName: "custom-stat-name"
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50001
        weight: 20
        name: "first-route-dest/backend/0"
      - endpoints:
        - host: "*******"
          port: 50002
        weight: 40
        name: "first-route-dest/backend/1"
      - endpoints:
        - host: "*******"
          port: 50003
        weight: 20
        name: "first-route-dest/backend/2"
      - endpoints:
        - host: "*******"
          port: 50004
        weight: 20
        name: "first-route-dest/backend/3"
