http:
  - name: "first-listener"
    address: "::"
    port: 10080
    hostnames:
      - "*"
    path:
      mergeSlashes: true
      escapedSlashesAction: UnescapeAndRedirect
    routes:
      - name: "first-route"
        hostname: "*"
        destination:
          name: "first-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "first-route-dest/backend/0"
              tls:
                insecureSkipVerify: true
                useSystemTrustStore: true
                CACertificate:
                  name: policy-btls/default-ca
                sni: example.com
