- clusterName: first-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
      metadata:
        filterMetadata:
          envoy.transport_socket_match:
            name: first-route-dest/tls/0
    loadBalancingWeight: 1
    locality:
      region: first-route-dest/backend/0
