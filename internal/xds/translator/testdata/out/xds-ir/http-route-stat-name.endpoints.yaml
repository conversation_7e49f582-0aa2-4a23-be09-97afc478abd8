- clusterName: first-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50001
      loadBalancingWeight: 1
    loadBalancingWeight: 20
    locality:
      region: first-route-dest/backend/0
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50002
      loadBalancingWeight: 1
    loadBalancingWeight: 40
    locality:
      region: first-route-dest/backend/1
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50003
      loadBalancingWeight: 1
    loadBalancingWeight: 20
    locality:
      region: first-route-dest/backend/2
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50004
      loadBalancingWeight: 1
    loadBalancingWeight: 20
    locality:
      region: first-route-dest/backend/3
