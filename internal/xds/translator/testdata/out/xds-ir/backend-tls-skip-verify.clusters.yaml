- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig:
    localityWeightedLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: first-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  transportSocketMatches:
  - match:
      name: first-route-dest/tls/0
    name: first-route-dest/tls/0
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        commonTlsContext: {}
        sni: example.com
  type: EDS
