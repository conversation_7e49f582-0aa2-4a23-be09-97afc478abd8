// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	discoveryv1 "k8s.io/api/discovery/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
	mcsapiv1a1 "sigs.k8s.io/mcs-api/pkg/apis/v1alpha1"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/utils"
)

// ControllerUtils provides utility methods for the controller
type ControllerUtils struct {
	reconciler *gatewayAPIReconciler
}

// NewControllerUtils creates a new ControllerUtils instance
func NewControllerUtils(reconciler *gatewayAPIReconciler) *ControllerUtils {
	return &ControllerUtils{reconciler: reconciler}
}

// GetNamespace retrieves a namespace by name
func (u *ControllerUtils) GetNamespace(ctx context.Context, name string) (*corev1.Namespace, error) {
	nsKey := types.NamespacedName{Name: name}
	ns := new(corev1.Namespace)
	if err := u.reconciler.client.Get(ctx, nsKey, ns); err != nil {
		u.reconciler.log.Error(err, "unable to get Namespace")
		return nil, err
	}
	return ns, nil
}

// ProcessOIDCHMACSecret adds the OIDC HMAC Secret to the resourceTree.
// The OIDC HMAC Secret is created by the CertGen job and is used by SecurityPolicy
// to configure OAuth2 filters.
func (u *ControllerUtils) ProcessOIDCHMACSecret(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) {
	var (
		secret corev1.Secret
		err    error
	)

	err = u.reconciler.client.Get(ctx,
		types.NamespacedName{Namespace: u.reconciler.namespace, Name: oidcHMACSecretName},
		&secret,
	)
	// We don't return an error here, because we want to continue reconciling
	// despite that the OIDC HMAC secret can't be found.
	// If the OIDC HMAC Secret is missing, the SecurityPolicy with OIDC will be
	// marked as invalid in its status when translating to IR.
	if err != nil {
		u.reconciler.log.Error(err,
			"failed to process OIDC HMAC Secret",
			"namespace", u.reconciler.namespace, "name", oidcHMACSecretName)
		return
	}

	key := utils.NamespacedName(&secret).String()
	if !resourceMap.allAssociatedSecrets.Has(key) {
		resourceMap.allAssociatedSecrets.Insert(key)
		resourceTree.Secrets = append(resourceTree.Secrets, &secret)
		u.reconciler.log.Info("processing OIDC HMAC Secret", "namespace", u.reconciler.namespace, "name", oidcHMACSecretName)
	}
}

// ProcessEnvoyTLSSecret adds the Envoy TLS Secret to the resourceTree.
// The Envoy TLS Secret is created by the CertGen job and is used by envoy to establish
// TLS connections to the rate limit service.
func (u *ControllerUtils) ProcessEnvoyTLSSecret(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) {
	var (
		secret corev1.Secret
		err    error
	)

	err = u.reconciler.client.Get(ctx,
		types.NamespacedName{Namespace: u.reconciler.namespace, Name: envoyTLSSecretName},
		&secret,
	)
	if err != nil {
		u.reconciler.log.Error(err,
			"failed to process Envoy TLS Secret",
			"namespace", u.reconciler.namespace, "name", envoyTLSSecretName)
		return
	}

	key := utils.NamespacedName(&secret).String()
	if !resourceMap.allAssociatedSecrets.Has(key) {
		resourceMap.allAssociatedSecrets.Insert(key)
		resourceTree.Secrets = append(resourceTree.Secrets, &secret)
		u.reconciler.log.Info("processing Envoy TLS Secret", "namespace", u.reconciler.namespace, "name", envoyTLSSecretName)
	}
}

// ProcessEnvoyProxySecretRef processes secret references in EnvoyProxy
func (u *ControllerUtils) ProcessEnvoyProxySecretRef(ctx context.Context, gwcResource *resource.Resources) {
	if gwcResource.EnvoyProxyForGatewayClass == nil ||
		gwcResource.EnvoyProxyForGatewayClass.Spec.BackendTLS == nil ||
		gwcResource.EnvoyProxyForGatewayClass.Spec.BackendTLS.ClientCertificateRef == nil {
		return
	}

	certRef := gwcResource.EnvoyProxyForGatewayClass.Spec.BackendTLS.ClientCertificateRef
	if refsSecret(certRef) {
		if err := u.reconciler.processSecretRef(
			ctx,
			newResourceMapping(),
			gwcResource,
			resource.KindGateway,
			gwcResource.EnvoyProxyForGatewayClass.Namespace,
			resource.KindEnvoyProxy,
			*certRef); err != nil {
			u.reconciler.log.Error(err,
				"failed to process TLS SecretRef for EnvoyProxy",
				"gateway", "issue", "secretRef", certRef)
		}
	}
}

// LogProcessingError logs an error during resource processing
func (u *ControllerUtils) LogProcessingError(err error, resourceType, gatewayClassName string) {
	u.reconciler.log.Error(err, fmt.Sprintf("failed %s for gatewayClass %s, skipping it", resourceType, gatewayClassName))
}

// LogInfo logs an informational message
func (u *ControllerUtils) LogInfo(msg string, keysAndValues ...interface{}) {
	u.reconciler.log.Info(msg, keysAndValues...)
}

// LogError logs an error message
func (u *ControllerUtils) LogError(err error, msg string, keysAndValues ...interface{}) {
	u.reconciler.log.Error(err, msg, keysAndValues...)
}

// IsNotFound checks if an error is a NotFound error
func (u *ControllerUtils) IsNotFound(err error) bool {
	return kerrors.IsNotFound(err)
}

// ResourceValidator provides validation methods for resources
type ResourceValidator struct {
	reconciler *gatewayAPIReconciler
}

// NewResourceValidator creates a new ResourceValidator instance
func NewResourceValidator(reconciler *gatewayAPIReconciler) *ResourceValidator {
	return &ResourceValidator{reconciler: reconciler}
}

// ValidateNamespaceLabels validates namespace labels for an object
func (v *ResourceValidator) ValidateNamespaceLabels(obj client.Object) (bool, error) {
	if v.reconciler.namespaceLabel == nil {
		return true, nil
	}
	return v.reconciler.checkObjectNamespaceLabels(obj)
}

// ValidateGatewayForReconcile validates if a gateway should be reconciled
func (v *ResourceValidator) ValidateGatewayForReconcile(gtw *gwapiv1.Gateway) bool {
	return v.reconciler.validateGatewayForReconcile(gtw)
}

// ValidateServiceImportForReconcile validates if a service import should be reconciled
func (v *ResourceValidator) ValidateServiceImportForReconcile(si *mcsapiv1a1.ServiceImport) bool {
	return v.reconciler.validateServiceImportForReconcile(si)
}

// ValidateEndpointSliceForReconcile validates if an endpoint slice should be reconciled
func (v *ResourceValidator) ValidateEndpointSliceForReconcile(eps *discoveryv1.EndpointSlice) bool {
	return v.reconciler.validateEndpointSliceForReconcile(eps)
}

// ValidateSecretForReconcile validates if a secret should be reconciled
func (v *ResourceValidator) ValidateSecretForReconcile(s *corev1.Secret) bool {
	return v.reconciler.validateSecretForReconcile(s)
}

// ValidateConfigMapForReconcile validates if a config map should be reconciled
func (v *ResourceValidator) ValidateConfigMapForReconcile(cm *corev1.ConfigMap) bool {
	return v.reconciler.validateConfigMapForReconcile(cm)
}

// ResourceMetrics provides metrics collection for resources
type ResourceMetrics struct {
	reconciler *gatewayAPIReconciler
}

// NewResourceMetrics creates a new ResourceMetrics instance
func NewResourceMetrics(reconciler *gatewayAPIReconciler) *ResourceMetrics {
	return &ResourceMetrics{reconciler: reconciler}
}

// RecordProcessingTime records the time taken to process a resource type
func (m *ResourceMetrics) RecordProcessingTime(resourceType string, duration time.Duration) {
	// Implementation would depend on the metrics framework being used
	// This is a placeholder for metrics collection
	m.reconciler.log.Info("resource processing completed",
		"resourceType", resourceType,
		"duration", duration.String())
}

// RecordResourceCount records the count of resources processed
func (m *ResourceMetrics) RecordResourceCount(resourceType string, count int) {
	// Implementation would depend on the metrics framework being used
	// This is a placeholder for metrics collection
	m.reconciler.log.Info("resource count",
		"resourceType", resourceType,
		"count", count)
}
