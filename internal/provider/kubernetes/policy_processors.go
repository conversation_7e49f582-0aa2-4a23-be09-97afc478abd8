// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"context"
	"fmt"

	gwapiv1a2 "sigs.k8s.io/gateway-api/apis/v1alpha2"
	gwapiv1a3 "sigs.k8s.io/gateway-api/apis/v1alpha3"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/utils"
)

// PolicyProcessor defines the interface for processing different types of policies
type PolicyProcessor interface {
	Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error
}

// EnvoyPatchPolicyProcessor processes EnvoyPatchPolicies
type EnvoyPatchPolicyProcessor struct {
	reconciler *gatewayAPIReconciler
}

func (p *EnvoyPatchPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
	envoyPatchPolicies := egv1a1.EnvoyPatchPolicyList{}
	if err := p.reconciler.client.List(ctx, &envoyPatchPolicies); err != nil {
		return fmt.Errorf("error listing EnvoyPatchPolicies: %w", err)
	}

	for _, policy := range envoyPatchPolicies.Items {
		envoyPatchPolicy := policy //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		envoyPatchPolicy.Status = gwapiv1a2.PolicyStatus{}
		if !resourceMap.allAssociatedEnvoyPatchPolicies.Has(utils.NamespacedName(&envoyPatchPolicy).String()) {
			resourceMap.allAssociatedEnvoyPatchPolicies.Insert(utils.NamespacedName(&envoyPatchPolicy).String())
			resourceTree.EnvoyPatchPolicies = append(resourceTree.EnvoyPatchPolicies, &envoyPatchPolicy)
		}
	}
	return nil
}

// ClientTrafficPolicyProcessor processes ClientTrafficPolicies
type ClientTrafficPolicyProcessor struct {
	reconciler *gatewayAPIReconciler
}

func (p *ClientTrafficPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
	clientTrafficPolicies := egv1a1.ClientTrafficPolicyList{}
	if err := p.reconciler.client.List(ctx, &clientTrafficPolicies); err != nil {
		return fmt.Errorf("error listing ClientTrafficPolicies: %w", err)
	}

	for _, policy := range clientTrafficPolicies.Items {
		clientTrafficPolicy := policy //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		clientTrafficPolicy.Status = gwapiv1a2.PolicyStatus{}
		if !resourceMap.allAssociatedClientTrafficPolicies.Has(utils.NamespacedName(&clientTrafficPolicy).String()) {
			resourceMap.allAssociatedClientTrafficPolicies.Insert(utils.NamespacedName(&clientTrafficPolicy).String())
			resourceTree.ClientTrafficPolicies = append(resourceTree.ClientTrafficPolicies, &clientTrafficPolicy)
		}
	}

	p.reconciler.processCtpConfigMapRefs(ctx, resourceTree, resourceMap)
	return nil
}

// BackendTrafficPolicyProcessor processes BackendTrafficPolicies
type BackendTrafficPolicyProcessor struct {
	reconciler *gatewayAPIReconciler
}

func (p *BackendTrafficPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
	backendTrafficPolicies := egv1a1.BackendTrafficPolicyList{}
	if err := p.reconciler.client.List(ctx, &backendTrafficPolicies); err != nil {
		return fmt.Errorf("error listing BackendTrafficPolicies: %w", err)
	}

	for _, policy := range backendTrafficPolicies.Items {
		backendTrafficPolicy := policy //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		backendTrafficPolicy.Status = gwapiv1a2.PolicyStatus{}
		if !resourceMap.allAssociatedBackendTrafficPolicies.Has(utils.NamespacedName(&backendTrafficPolicy).String()) {
			resourceMap.allAssociatedBackendTrafficPolicies.Insert(utils.NamespacedName(&backendTrafficPolicy).String())
			resourceTree.BackendTrafficPolicies = append(resourceTree.BackendTrafficPolicies, &backendTrafficPolicy)
		}
	}
	p.reconciler.processBtpConfigMapRefs(ctx, resourceTree, resourceMap)
	return nil
}

// SecurityPolicyProcessor processes SecurityPolicies
type SecurityPolicyProcessor struct {
	reconciler *gatewayAPIReconciler
}

func (p *SecurityPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
	securityPolicies := egv1a1.SecurityPolicyList{}
	if err := p.reconciler.client.List(ctx, &securityPolicies); err != nil {
		return fmt.Errorf("error listing SecurityPolicies: %w", err)
	}

	for _, policy := range securityPolicies.Items {
		securityPolicy := policy //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		securityPolicy.Status = gwapiv1a2.PolicyStatus{}
		if !resourceMap.allAssociatedSecurityPolicies.Has(utils.NamespacedName(&securityPolicy).String()) {
			resourceMap.allAssociatedSecurityPolicies.Insert(utils.NamespacedName(&securityPolicy).String())
			resourceTree.SecurityPolicies = append(resourceTree.SecurityPolicies, &securityPolicy)
		}
	}

	// Add the referenced Resources in SecurityPolicies to the resourceTree
	p.reconciler.processSecurityPolicyObjectRefs(ctx, resourceTree, resourceMap)

	// Add the OIDC HMAC Secret to the resourceTree
	p.reconciler.processOIDCHMACSecret(ctx, resourceTree, resourceMap)

	// Add the Envoy TLS Secret to the resourceTree
	p.reconciler.processEnvoyTLSSecret(ctx, resourceTree, resourceMap)
	return nil
}

// BackendTLSPolicyProcessor processes BackendTLSPolicies
type BackendTLSPolicyProcessor struct {
	reconciler *gatewayAPIReconciler
}

func (p *BackendTLSPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
	backendTLSPolicies := gwapiv1a3.BackendTLSPolicyList{}
	if err := p.reconciler.client.List(ctx, &backendTLSPolicies); err != nil {
		return fmt.Errorf("error listing BackendTLSPolicies: %w", err)
	}

	for _, policy := range backendTLSPolicies.Items {
		backendTLSPolicy := policy //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		backendTLSPolicy.Status = gwapiv1a2.PolicyStatus{}
		if !resourceMap.allAssociatedBackendTLSPolicies.Has(utils.NamespacedName(&backendTLSPolicy).String()) {
			resourceMap.allAssociatedBackendTLSPolicies.Insert(utils.NamespacedName(&backendTLSPolicy).String())
			resourceTree.BackendTLSPolicies = append(resourceTree.BackendTLSPolicies, &backendTLSPolicy)
		}
	}

	// Add the referenced Secrets and ConfigMaps in BackendTLSPolicies to the resourceTree.
	p.reconciler.processBackendTLSPolicyRefs(ctx, resourceTree, resourceMap)
	return nil
}

// EnvoyExtensionPolicyProcessor processes EnvoyExtensionPolicies
type EnvoyExtensionPolicyProcessor struct {
	reconciler *gatewayAPIReconciler
}

func (p *EnvoyExtensionPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
	envoyExtensionPolicies := egv1a1.EnvoyExtensionPolicyList{}
	if err := p.reconciler.client.List(ctx, &envoyExtensionPolicies); err != nil {
		return fmt.Errorf("error listing EnvoyExtensionPolicies: %w", err)
	}

	for _, policy := range envoyExtensionPolicies.Items {
		envoyExtensionPolicy := policy //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		envoyExtensionPolicy.Status = gwapiv1a2.PolicyStatus{}
		if !resourceMap.allAssociatedEnvoyExtensionPolicies.Has(utils.NamespacedName(&envoyExtensionPolicy).String()) {
			resourceMap.allAssociatedEnvoyExtensionPolicies.Insert(utils.NamespacedName(&envoyExtensionPolicy).String())
			resourceTree.EnvoyExtensionPolicies = append(resourceTree.EnvoyExtensionPolicies, &envoyExtensionPolicy)
		}
	}

	// Add the referenced Resources in EnvoyExtensionPolicies to the resourceTree
	p.reconciler.processEnvoyExtensionPolicyObjectRefs(ctx, resourceTree, resourceMap)
	return nil
}

// PolicyProcessorFactory creates policy processors
type PolicyProcessorFactory struct {
	reconciler *gatewayAPIReconciler
}

func NewPolicyProcessorFactory(reconciler *gatewayAPIReconciler) *PolicyProcessorFactory {
	return &PolicyProcessorFactory{reconciler: reconciler}
}

func (f *PolicyProcessorFactory) CreateEnvoyPatchPolicyProcessor() PolicyProcessor {
	return &EnvoyPatchPolicyProcessor{reconciler: f.reconciler}
}

func (f *PolicyProcessorFactory) CreateClientTrafficPolicyProcessor() PolicyProcessor {
	return &ClientTrafficPolicyProcessor{reconciler: f.reconciler}
}

func (f *PolicyProcessorFactory) CreateBackendTrafficPolicyProcessor() PolicyProcessor {
	return &BackendTrafficPolicyProcessor{reconciler: f.reconciler}
}

func (f *PolicyProcessorFactory) CreateSecurityPolicyProcessor() PolicyProcessor {
	return &SecurityPolicyProcessor{reconciler: f.reconciler}
}

func (f *PolicyProcessorFactory) CreateBackendTLSPolicyProcessor() PolicyProcessor {
	return &BackendTLSPolicyProcessor{reconciler: f.reconciler}
}

func (f *PolicyProcessorFactory) CreateEnvoyExtensionPolicyProcessor() PolicyProcessor {
	return &EnvoyExtensionPolicyProcessor{reconciler: f.reconciler}
}
