// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/fields"
	"sigs.k8s.io/controller-runtime/pkg/client"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/gatewayapi/status"
	"github.com/envoyproxy/gateway/internal/utils"
)

// gatewayProcessor handles processing of Gateway resources and their associated routes
type gatewayProcessor struct {
	reconciler *gatewayAPIReconciler
}

// NewGatewayProcessor creates a new GatewayProcessor
func NewGatewayProcessor(reconciler *gatewayAPIReconciler) GatewayProcessor {
	return &gatewayProcessor{reconciler: reconciler}
}

// ProcessGateways processes all gateways for a given GatewayClass
func (p *gatewayProcessor) ProcessGateways(ctx context.Context, managedGC *gwapiv1.GatewayClass, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	// Find gateways for the managedGC
	gatewayList := &gwapiv1.GatewayList{}
	if err := p.reconciler.client.List(ctx, gatewayList, &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector(classGatewayIndex, managedGC.Name),
	}); err != nil {
		p.reconciler.log.Error(err, "failed to list gateways for gatewayClass %s", managedGC.Name)
		return err
	}

	for _, gtw := range gatewayList.Items {
		gtw := gtw //nolint:copyloopvar
		if err := p.processGateway(ctx, &gtw, resourceMap, resourceTree); err != nil {
			p.reconciler.log.Error(err, "failed to process gateway", "namespace", gtw.Namespace, "name", gtw.Name)
			continue
		}
	}

	return nil
}

// processGateway processes a single Gateway and its associated resources
func (p *gatewayProcessor) processGateway(ctx context.Context, gtw *gwapiv1.Gateway, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	// Check namespace labels if required
	if p.reconciler.namespaceLabel != nil {
		if ok, err := p.reconciler.checkObjectNamespaceLabels(gtw); err != nil {
			return fmt.Errorf("failed to check namespace labels for gateway %s in namespace %s: %w", gtw.GetName(), gtw.GetNamespace(), err)
		} else if !ok {
			return nil // Skip this gateway
		}
	}

	p.reconciler.log.Info("processing Gateway", "namespace", gtw.Namespace, "name", gtw.Name)
	resourceMap.allAssociatedNamespaces.Insert(gtw.Namespace)

	// Process TLS certificates
	if err := p.processTLSCertificates(ctx, gtw, resourceMap, resourceTree); err != nil {
		return fmt.Errorf("failed to process TLS certificates: %w", err)
	}

	// Process routes
	if err := p.processRoutes(ctx, gtw, resourceMap, resourceTree); err != nil {
		return fmt.Errorf("failed to process routes: %w", err)
	}

	// Process gateway parameters
	if err := p.processGatewayParameters(ctx, gtw, resourceMap, resourceTree); err != nil {
		// Update the Gateway status to not accepted if there is an error processing the parametersRef.
		status.UpdateGatewayStatusNotAccepted(gtw, gwapiv1.GatewayReasonInvalidParameters, err.Error())
		p.reconciler.log.Error(err, "failed to process infrastructure.parametersRef for gateway", "namespace", gtw.Namespace, "name", gtw.Name)
	}

	// Add gateway to resource tree
	gtwNamespacedName := utils.NamespacedName(gtw).String()
	if !resourceMap.allAssociatedGateways.Has(gtwNamespacedName) {
		resourceMap.allAssociatedGateways.Insert(gtwNamespacedName)
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		gtw.Status = gwapiv1.GatewayStatus{}
		resourceTree.Gateways = append(resourceTree.Gateways, gtw)
	}

	return nil
}

// processTLSCertificates processes TLS certificates for gateway listeners
func (p *gatewayProcessor) processTLSCertificates(ctx context.Context, gtw *gwapiv1.Gateway, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	for _, listener := range gtw.Spec.Listeners {
		// Get Secret for gateway if it exists.
		if terminatesTLS(&listener) {
			for _, certRef := range listener.TLS.CertificateRefs {
				if refsSecret(&certRef) {
					if err := p.reconciler.processSecretRef(
						ctx,
						resourceMap,
						resourceTree,
						resource.KindGateway,
						gtw.Namespace,
						gtw.Name,
						certRef); err != nil {
						p.reconciler.log.Error(err,
							"failed to process TLS SecretRef for gateway",
							"gateway", gtw, "secretRef", certRef)
					}
				}
			}
		}
	}
	return nil
}

// processRoutes processes all route types for a gateway
func (p *gatewayProcessor) processRoutes(ctx context.Context, gtw *gwapiv1.Gateway, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	gtwNamespacedName := utils.NamespacedName(gtw).String()

	// Process different route types based on CRD existence
	if p.reconciler.tlsRouteCRDExists {
		if err := p.reconciler.processTLSRoutes(ctx, gtwNamespacedName, resourceMap, resourceTree); err != nil {
			return fmt.Errorf("failed to process TLS routes: %w", err)
		}
	}

	// Get HTTPRoute objects and check if it exists.
	if err := p.reconciler.processHTTPRoutes(ctx, gtwNamespacedName, resourceMap, resourceTree); err != nil {
		return fmt.Errorf("failed to process HTTP routes: %w", err)
	}

	if p.reconciler.grpcRouteCRDExists {
		if err := p.reconciler.processGRPCRoutes(ctx, gtwNamespacedName, resourceMap, resourceTree); err != nil {
			return fmt.Errorf("failed to process GRPC routes: %w", err)
		}
	}

	if p.reconciler.tcpRouteCRDExists {
		if err := p.reconciler.processTCPRoutes(ctx, gtwNamespacedName, resourceMap, resourceTree); err != nil {
			return fmt.Errorf("failed to process TCP routes: %w", err)
		}
	}

	if p.reconciler.udpRouteCRDExists {
		if err := p.reconciler.processUDPRoutes(ctx, gtwNamespacedName, resourceMap, resourceTree); err != nil {
			return fmt.Errorf("failed to process UDP routes: %w", err)
		}
	}

	return nil
}

// processGatewayParameters processes gateway infrastructure parameters
func (p *gatewayProcessor) processGatewayParameters(ctx context.Context, gtw *gwapiv1.Gateway, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	return p.reconciler.processGatewayParamsRef(ctx, gtw, resourceMap, resourceTree)
}

// routeProcessor handles processing of different route types
type routeProcessor struct {
	reconciler *gatewayAPIReconciler
}

// NewRouteProcessor creates a new RouteProcessor
func NewRouteProcessor(reconciler *gatewayAPIReconciler) RouteProcessor {
	return &routeProcessor{reconciler: reconciler}
}

// ProcessHTTPRoutes processes HTTPRoute resources
func (p *routeProcessor) ProcessHTTPRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	return p.reconciler.processHTTPRoutes(ctx, gatewayNamespacedName, resourceMap, resourceTree)
}

// ProcessGRPCRoutes processes GRPCRoute resources
func (p *routeProcessor) ProcessGRPCRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	return p.reconciler.processGRPCRoutes(ctx, gatewayNamespacedName, resourceMap, resourceTree)
}

// ProcessTLSRoutes processes TLSRoute resources
func (p *routeProcessor) ProcessTLSRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	return p.reconciler.processTLSRoutes(ctx, gatewayNamespacedName, resourceMap, resourceTree)
}

// ProcessTCPRoutes processes TCPRoute resources
func (p *routeProcessor) ProcessTCPRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	return p.reconciler.processTCPRoutes(ctx, gatewayNamespacedName, resourceMap, resourceTree)
}

// ProcessUDPRoutes processes UDPRoute resources
func (p *routeProcessor) ProcessUDPRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error {
	return p.reconciler.processUDPRoutes(ctx, gatewayNamespacedName, resourceMap, resourceTree)
}

// GatewayProcessorFactory creates gateway and route processors
type GatewayProcessorFactory struct {
	reconciler *gatewayAPIReconciler
}

// NewGatewayProcessorFactory creates a new GatewayProcessorFactory
func NewGatewayProcessorFactory(reconciler *gatewayAPIReconciler) *GatewayProcessorFactory {
	return &GatewayProcessorFactory{reconciler: reconciler}
}

// CreateGatewayProcessor creates a new GatewayProcessor
func (f *GatewayProcessorFactory) CreateGatewayProcessor() GatewayProcessor {
	return NewGatewayProcessor(f.reconciler)
}

// CreateRouteProcessor creates a new RouteProcessor
func (f *GatewayProcessorFactory) CreateRouteProcessor() RouteProcessor {
	return NewRouteProcessor(f.reconciler)
}
