// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"context"
	"fmt"

	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/gatewayapi"
	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/utils"
)

// processBackends adds Backends to the resourceTree
func (r *gatewayAPIReconciler) processBackends(ctx context.Context, resourceTree *resource.Resources) error {
	backends := egv1a1.BackendList{}
	if err := r.client.List(ctx, &backends); err != nil {
		return fmt.Errorf("error listing Backends: %w", err)
	}

	for _, backend := range backends.Items {
		backend := backend //nolint:copyloopvar
		// Discard Status to reduce memory consumption in watchable
		// It will be recomputed by the gateway-api layer
		backend.Status = egv1a1.BackendStatus{}
		resourceTree.Backends = append(resourceTree.Backends, &backend)
	}
	return nil
}

// processBackendTLSPolicyRefs adds the referenced Secrets and ConfigMaps in BackendTLSPolicies to the resourceTree.
func (r *gatewayAPIReconciler) processBackendTLSPolicyRefs(
	ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings,
) {
	for _, policy := range resourceTree.BackendTLSPolicies {
		// Add the referenced CACertificateRefs in BackendTLSPolicies to the resourceTree
		if policy.Spec.Validation.CACertificateRefs != nil {
			for _, caCertRef := range policy.Spec.Validation.CACertificateRefs {
				// if kind is not Secret or ConfigMap, we skip early to avoid further calculation overhead
				if string(caCertRef.Kind) != resource.KindConfigMap && string(caCertRef.Kind) != resource.KindSecret {
					continue
				}

				caRefNew := gwapiv1.SecretObjectReference{
					Group:     gatewayapi.GroupPtr(string(caCertRef.Group)),
					Kind:      gatewayapi.KindPtr(string(caCertRef.Kind)),
					Name:      caCertRef.Name,
					Namespace: gatewayapi.NamespacePtr(policy.Namespace),
				}

				var err error
				switch string(caCertRef.Kind) {
				case resource.KindConfigMap:
					err = r.processConfigMapRef(
						ctx,
						resourceMap,
						resourceTree,
						resource.KindBackendTLSPolicy,
						policy.Namespace,
						policy.Name,
						caRefNew)
				case resource.KindSecret:
					err = r.processSecretRef(
						ctx,
						resourceMap,
						resourceTree,
						resource.KindBackendTLSPolicy,
						policy.Namespace,
						policy.Name,
						caRefNew)
				}

				if err != nil {
					// we don't return an error here, because we want to continue
					// reconciling the rest of the BackendTLSPolicies despite that this
					// reference is invalid.
					// This BackendTLSPolicy will be marked as invalid in its status
					// when translating to IR because the referenced secret/configmap can't be
					// found.
					r.log.Error(err,
						"failed to process CACertificateRef for BackendTLSPolicy",
						"policy", policy, "caCertificateRef", caCertRef.Name)
				}
			}
		}

		// Add the referenced WellKnownCACertificates in BackendTLSPolicies to the resourceTree
		if policy.Spec.Validation.WellKnownCACertificates != nil {
			// WellKnownCACertificates doesn't require any additional processing
			// as it's a predefined set of CA certificates
			r.log.Info("processing WellKnownCACertificates for BackendTLSPolicy",
				"policy", utils.NamespacedName(policy),
				"wellKnownCACerts", *policy.Spec.Validation.WellKnownCACertificates)
		}
	}
}
