# Usage Examples for Refactored Kubernetes Controller

This document provides examples of how to use the refactored Kubernetes controller components.

## 1. Using Policy Processors

### Creating and Using a Policy Processor

```go
// Create a policy processor factory
factory := NewPolicyProcessorFactory(reconciler)

// Process EnvoyPatchPolicies
if reconciler.eppCRDExists {
    processor := factory.CreateEnvoyPatchPolicyProcessor()
    if err := processor.Process(ctx, gwcResource, resourceMappings); err != nil {
        return fmt.Errorf("failed to process EnvoyPatchPolicies: %w", err)
    }
}

// Process SecurityPolicies
if reconciler.spCRDExists {
    processor := factory.CreateSecurityPolicyProcessor()
    if err := processor.Process(ctx, gwcResource, resourceMappings); err != nil {
        return fmt.Errorf("failed to process SecurityPolicies: %w", err)
    }
}
```

### Implementing a Custom Policy Processor

```go
// CustomPolicyProcessor implements the PolicyProcessor interface
type CustomPolicyProcessor struct {
    reconciler *gatewayAPIReconciler
}

func (p *CustomPolicyProcessor) Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error {
    // Custom policy processing logic
    customPolicies := CustomPolicyList{}
    if err := p.reconciler.client.List(ctx, &customPolicies); err != nil {
        return fmt.Errorf("error listing CustomPolicies: %w", err)
    }

    for _, policy := range customPolicies.Items {
        // Process each policy
        // Add to resource tree
        resourceTree.CustomPolicies = append(resourceTree.CustomPolicies, &policy)
    }
    
    return nil
}
```

## 2. Using Gateway Processor

### Processing Gateways and Routes

```go
// Create a gateway processor
gatewayProcessor := NewGatewayProcessor(reconciler)

// Process all gateways for a GatewayClass
if err := gatewayProcessor.ProcessGateways(ctx, managedGC, resourceMappings, gwcResource); err != nil {
    return fmt.Errorf("failed to process gateways: %w", err)
}

// Create a route processor for specific route types
routeProcessor := NewRouteProcessor(reconciler)

// Process HTTP routes
if err := routeProcessor.ProcessHTTPRoutes(ctx, gatewayNamespacedName, resourceMappings, gwcResource); err != nil {
    return fmt.Errorf("failed to process HTTP routes: %w", err)
}
```

### Using Gateway Processor Factory

```go
// Create a gateway processor factory
factory := NewGatewayProcessorFactory(reconciler)

// Create processors
gatewayProcessor := factory.CreateGatewayProcessor()
routeProcessor := factory.CreateRouteProcessor()

// Use the processors
if err := gatewayProcessor.ProcessGateways(ctx, managedGC, resourceMappings, gwcResource); err != nil {
    log.Error(err, "failed to process gateways")
}
```

## 3. Using Reference Processor

### Processing Backend References

```go
// Create a reference processor
referenceProcessor := NewReferenceProcessor(reconciler)

// Process all backend references
referenceProcessor.ProcessBackendRefs(ctx, gwcResource, resourceMappings)

// Process a specific backend reference
backendRef := gwapiv1.BackendObjectReference{
    Kind:      gatewayapi.KindPtr("Service"),
    Name:      "my-service",
    Namespace: gatewayapi.NamespacePtr("default"),
}

if err := referenceProcessor.ProcessBackendRef(
    ctx, resourceMappings, gwcResource,
    "HTTPRoute", "default", "my-route", backendRef,
); err != nil {
    log.Error(err, "failed to process backend reference")
}
```

## 4. Using Controller Utils

### Basic Utility Operations

```go
// Create controller utils
utils := NewControllerUtils(reconciler)

// Get a namespace
namespace, err := utils.GetNamespace(ctx, "default")
if err != nil {
    log.Error(err, "failed to get namespace")
}

// Process OIDC HMAC secret
utils.ProcessOIDCHMACSecret(ctx, resourceTree, resourceMappings)

// Process Envoy TLS secret
utils.ProcessEnvoyTLSSecret(ctx, resourceTree, resourceMappings)

// Process EnvoyProxy secret references
utils.ProcessEnvoyProxySecretRef(ctx, gwcResource)
```

### Using Resource Validator

```go
// Create a resource validator
validator := NewResourceValidator(reconciler)

// Validate namespace labels
if ok, err := validator.ValidateNamespaceLabels(gateway); err != nil {
    log.Error(err, "failed to validate namespace labels")
} else if !ok {
    log.Info("gateway does not match namespace selector")
    return // Skip processing
}

// Validate gateway for reconciliation
if !validator.ValidateGatewayForReconcile(gateway) {
    log.Info("gateway should not be reconciled")
    return
}
```

### Using Resource Metrics

```go
// Create resource metrics
metrics := NewResourceMetrics(reconciler)

// Record processing time
start := time.Now()
// ... do processing ...
duration := time.Since(start)
metrics.RecordProcessingTime("HTTPRoute", duration)

// Record resource count
metrics.RecordResourceCount("SecurityPolicy", len(securityPolicies))
```

## 5. Complete Example: Processing a GatewayClass

```go
func (r *gatewayAPIReconciler) processGatewayClassExample(ctx context.Context, managedGC *gwapiv1.GatewayClass) (*resource.Resources, error) {
    // Initialize resources
    gwcResource := resource.NewResources()
    gwcResource.GatewayClass = managedGC
    resourceMappings := newResourceMapping()

    // Create processors
    gatewayProcessor := NewGatewayProcessor(r)
    referenceProcessor := NewReferenceProcessor(r)
    policyFactory := NewPolicyProcessorFactory(r)
    utils := NewControllerUtils(r)
    validator := NewResourceValidator(r)
    metrics := NewResourceMetrics(r)

    start := time.Now()

    // Process gateways
    if err := gatewayProcessor.ProcessGateways(ctx, managedGC, resourceMappings, gwcResource); err != nil {
        return nil, fmt.Errorf("failed to process gateways: %w", err)
    }

    // Process policies
    if r.spCRDExists {
        processor := policyFactory.CreateSecurityPolicyProcessor()
        if err := processor.Process(ctx, gwcResource, resourceMappings); err != nil {
            return nil, fmt.Errorf("failed to process security policies: %w", err)
        }
    }

    // Process backend references
    referenceProcessor.ProcessBackendRefs(ctx, gwcResource, resourceMappings)

    // Process namespaces
    for ns := range resourceMappings.allAssociatedNamespaces {
        namespace, err := utils.GetNamespace(ctx, ns)
        if err != nil {
            if utils.IsNotFound(err) {
                continue
            }
            return nil, fmt.Errorf("failed to get namespace %s: %w", ns, err)
        }
        gwcResource.Namespaces = append(gwcResource.Namespaces, namespace)
    }

    // Process secrets
    utils.ProcessOIDCHMACSecret(ctx, gwcResource, resourceMappings)
    utils.ProcessEnvoyTLSSecret(ctx, gwcResource, resourceMappings)
    utils.ProcessEnvoyProxySecretRef(ctx, gwcResource)

    // Record metrics
    duration := time.Since(start)
    metrics.RecordProcessingTime("GatewayClass", duration)
    metrics.RecordResourceCount("Gateway", len(gwcResource.Gateways))

    return gwcResource, nil
}
```

## 6. Error Handling Patterns

### Graceful Error Handling

```go
func processWithErrorHandling(ctx context.Context, processor PolicyProcessor, gwcResource *resource.Resources, resourceMappings *resourceMappings) error {
    if err := processor.Process(ctx, gwcResource, resourceMappings); err != nil {
        // Log the error but continue processing
        log.Error(err, "failed to process policy", "processorType", fmt.Sprintf("%T", processor))
        return nil // Don't fail the entire reconciliation
    }
    return nil
}
```

### Validation with Early Return

```go
func validateAndProcess(ctx context.Context, gateway *gwapiv1.Gateway, validator *ResourceValidator) error {
    // Validate namespace labels
    if ok, err := validator.ValidateNamespaceLabels(gateway); err != nil {
        return fmt.Errorf("validation error: %w", err)
    } else if !ok {
        return nil // Skip processing, but not an error
    }

    // Validate gateway for reconciliation
    if !validator.ValidateGatewayForReconcile(gateway) {
        return nil // Skip processing
    }

    // Continue with processing...
    return processGateway(ctx, gateway)
}
```

## Benefits of the Refactored Architecture

1. **Modularity**: Each processor handles a specific type of resource
2. **Testability**: Individual processors can be unit tested in isolation
3. **Extensibility**: New processors can be added without modifying existing code
4. **Maintainability**: Clear separation of concerns makes the code easier to maintain
5. **Reusability**: Processors can be reused in different contexts
6. **Error Handling**: Better error isolation and handling
7. **Performance**: Metrics collection helps identify performance bottlenecks
