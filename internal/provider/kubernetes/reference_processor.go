// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	discoveryv1 "k8s.io/api/discovery/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
	gwapiv1b1 "sigs.k8s.io/gateway-api/apis/v1beta1"
	mcsapiv1a1 "sigs.k8s.io/mcs-api/pkg/apis/v1alpha1"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/gatewayapi"
	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/utils"
)

// ReferenceProcessor handles processing of cross-namespace references and ReferenceGrants
type ReferenceProcessor struct {
	reconciler *gatewayAPIReconciler
}

// NewReferenceProcessor creates a new ReferenceProcessor
func NewReferenceProcessor(reconciler *gatewayAPIReconciler) *ReferenceProcessor {
	return &ReferenceProcessor{reconciler: reconciler}
}

// ProcessBackendRefs processes all backend references and their associated resources
func (p *ReferenceProcessor) ProcessBackendRefs(ctx context.Context, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	for backendRef := range resourceMappings.allAssociatedBackendRefs {
		p.processBackendRef(ctx, backendRef, gwcResource, resourceMappings)
	}
}

// processBackendRef processes a single backend reference
func (p *ReferenceProcessor) processBackendRef(ctx context.Context, backendRef gwapiv1.BackendObjectReference, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	backendRefKind := gatewayapi.KindDerefOr(backendRef.Kind, resource.KindService)
	p.reconciler.log.Info("processing Backend", "kind", backendRefKind, "namespace", string(*backendRef.Namespace),
		"name", string(backendRef.Name))

	switch backendRefKind {
	case resource.KindService:
		p.processServiceBackend(ctx, backendRef, gwcResource, resourceMappings)
	case resource.KindServiceImport:
		p.processServiceImportBackend(ctx, backendRef, gwcResource, resourceMappings)
	case egv1a1.KindBackend:
		p.processCustomBackend(ctx, backendRef, gwcResource, resourceMappings)
	}
}

// processServiceBackend processes Service backend references
func (p *ReferenceProcessor) processServiceBackend(ctx context.Context, backendRef gwapiv1.BackendObjectReference, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	service := new(corev1.Service)
	err := p.reconciler.client.Get(ctx, types.NamespacedName{Namespace: string(*backendRef.Namespace), Name: string(backendRef.Name)}, service)
	if err != nil {
		p.reconciler.log.Error(err, "failed to get Service", "namespace", string(*backendRef.Namespace),
			"name", string(backendRef.Name))
		return
	}

	resourceMappings.allAssociatedNamespaces.Insert(service.Namespace)
	gwcResource.Services = append(gwcResource.Services, service)
	p.reconciler.log.Info("added Service to resource tree", "namespace", string(*backendRef.Namespace),
		"name", string(backendRef.Name))

	// Process EndpointSlices for the Service
	p.processEndpointSlices(ctx, backendRef, discoveryv1.LabelServiceName, gwcResource, resourceMappings)
}

// processServiceImportBackend processes ServiceImport backend references
func (p *ReferenceProcessor) processServiceImportBackend(ctx context.Context, backendRef gwapiv1.BackendObjectReference, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	serviceImport := new(mcsapiv1a1.ServiceImport)
	err := p.reconciler.client.Get(ctx, types.NamespacedName{Namespace: string(*backendRef.Namespace), Name: string(backendRef.Name)}, serviceImport)
	if err != nil {
		p.reconciler.log.Error(err, "failed to get ServiceImport", "namespace", string(*backendRef.Namespace),
			"name", string(backendRef.Name))
		return
	}

	resourceMappings.allAssociatedNamespaces.Insert(serviceImport.Namespace)
	key := utils.NamespacedName(serviceImport).String()
	if !resourceMappings.allAssociatedServiceImports.Has(key) {
		resourceMappings.allAssociatedServiceImports.Insert(key)
		gwcResource.ServiceImports = append(gwcResource.ServiceImports, serviceImport)
		p.reconciler.log.Info("added ServiceImport to resource tree", "namespace", string(*backendRef.Namespace),
			"name", string(backendRef.Name))
	}

	// Process EndpointSlices for the ServiceImport
	p.processEndpointSlices(ctx, backendRef, mcsapiv1a1.LabelServiceName, gwcResource, resourceMappings)
}

// processCustomBackend processes custom Backend references
func (p *ReferenceProcessor) processCustomBackend(ctx context.Context, backendRef gwapiv1.BackendObjectReference, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	backend := new(egv1a1.Backend)
	err := p.reconciler.client.Get(ctx, types.NamespacedName{Namespace: string(*backendRef.Namespace), Name: string(backendRef.Name)}, backend)
	if err != nil {
		p.reconciler.log.Error(err, "failed to get Backend", "namespace", string(*backendRef.Namespace),
			"name", string(backendRef.Name))
		return
	}

	resourceMappings.allAssociatedNamespaces.Insert(backend.Namespace)
	key := utils.NamespacedName(backend).String()
	if !resourceMappings.allAssociatedBackends.Has(key) {
		resourceMappings.allAssociatedBackends.Insert(key)
		gwcResource.Backends = append(gwcResource.Backends, backend)
		p.reconciler.log.Info("added Backend to resource tree", "namespace", string(*backendRef.Namespace),
			"name", string(backendRef.Name))
	}

	// Process CA certificate references in the Backend
	p.processBackendCACertificateRefs(ctx, backend, gwcResource, resourceMappings)
}

// processBackendCACertificateRefs processes CA certificate references in Backend
func (p *ReferenceProcessor) processBackendCACertificateRefs(ctx context.Context, backend *egv1a1.Backend, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	if backend.Spec.TLS == nil || backend.Spec.TLS.CACertificateRefs == nil {
		return
	}

	for _, caCertRef := range backend.Spec.TLS.CACertificateRefs {
		// if kind is not Secret or ConfigMap, we skip early to avoid further calculation overhead
		if string(caCertRef.Kind) != resource.KindConfigMap && string(caCertRef.Kind) != resource.KindSecret {
			continue
		}

		caRefNew := gwapiv1.SecretObjectReference{
			Group:     gatewayapi.GroupPtr(string(caCertRef.Group)),
			Kind:      gatewayapi.KindPtr(string(caCertRef.Kind)),
			Name:      caCertRef.Name,
			Namespace: gatewayapi.NamespacePtr(backend.Namespace),
		}

		var err error
		switch string(caCertRef.Kind) {
		case resource.KindConfigMap:
			err = p.reconciler.processConfigMapRef(
				ctx,
				resourceMappings,
				gwcResource,
				resource.KindBackendTLSPolicy,
				backend.Namespace,
				backend.Name,
				caRefNew)
		case resource.KindSecret:
			err = p.reconciler.processSecretRef(
				ctx,
				resourceMappings,
				gwcResource,
				resource.KindBackendTLSPolicy,
				backend.Namespace,
				backend.Name,
				caRefNew)
		}

		if err != nil {
			p.reconciler.log.Error(err,
				"failed to process CACertificateRef for Backend",
				"backend", backend, "caCertificateRef", caCertRef.Name)
		}
	}
}

// processEndpointSlices processes EndpointSlices for a backend reference
func (p *ReferenceProcessor) processEndpointSlices(ctx context.Context, backendRef gwapiv1.BackendObjectReference, labelKey string, gwcResource *resource.Resources, resourceMappings *resourceMappings) {
	endpointSliceList := new(discoveryv1.EndpointSliceList)
	opts := []client.ListOption{
		client.MatchingLabels(map[string]string{
			labelKey: string(backendRef.Name),
		}),
		client.InNamespace(*backendRef.Namespace),
	}

	if err := p.reconciler.client.List(ctx, endpointSliceList, opts...); err != nil {
		p.reconciler.log.Error(err, "failed to get EndpointSlices", "namespace", string(*backendRef.Namespace),
			"backendKind", gatewayapi.KindDerefOr(backendRef.Kind, resource.KindService), "name", string(backendRef.Name))
		return
	}

	for _, endpointSlice := range endpointSliceList.Items {
		key := utils.NamespacedName(&endpointSlice).String()
		if !resourceMappings.allAssociatedEndpointSlices.Has(key) {
			resourceMappings.allAssociatedEndpointSlices.Insert(key)
			p.reconciler.log.Info("added EndpointSlice to resource tree",
				"namespace", endpointSlice.Namespace,
				"name", endpointSlice.Name)
			gwcResource.EndpointSlices = append(gwcResource.EndpointSlices, &endpointSlice)
		}
	}
}

// ProcessBackendRef adds the referenced BackendRef to the resourceMap for later processBackendRefs.
// If BackendRef exists in a different namespace and there is a ReferenceGrant, adds ReferenceGrant to the resourceTree.
func (p *ReferenceProcessor) ProcessBackendRef(
	ctx context.Context,
	resourceMap *resourceMappings,
	resourceTree *resource.Resources,
	ownerKind string,
	ownerNS string,
	ownerName string,
	backendRef gwapiv1.BackendObjectReference,
) error {
	backendNamespace := gatewayapi.NamespaceDerefOr(backendRef.Namespace, ownerNS)
	resourceMap.allAssociatedBackendRefs.Insert(gwapiv1.BackendObjectReference{
		Group:     backendRef.Group,
		Kind:      backendRef.Kind,
		Namespace: gatewayapi.NamespacePtr(backendNamespace),
		Name:      backendRef.Name,
	})

	if backendNamespace != ownerNS {
		return p.processReferenceGrant(ctx, resourceMap, resourceTree, ownerKind, ownerNS, ownerName, backendNamespace, string(backendRef.Name), gatewayapi.KindDerefOr(backendRef.Kind, resource.KindService))
	}
	return nil
}

// processReferenceGrant processes ReferenceGrant for cross-namespace references
func (p *ReferenceProcessor) processReferenceGrant(ctx context.Context, resourceMap *resourceMappings, resourceTree *resource.Resources, fromKind, fromNS, fromName, toNS, toName, toKind string) error {
	from := ObjectKindNamespacedName{
		kind:      fromKind,
		namespace: fromNS,
		name:      fromName,
	}
	to := ObjectKindNamespacedName{
		kind:      toKind,
		namespace: toNS,
		name:      toName,
	}

	refGrant, err := p.findReferenceGrant(ctx, from, to)
	switch {
	case err != nil:
		return fmt.Errorf("failed to find ReferenceGrant: %w", err)
	case refGrant == nil:
		return fmt.Errorf("no matching ReferenceGrants found: from %s/%s to %s/%s", from.kind, from.namespace, to.kind, to.namespace)
	default:
		// RefGrant found
		if !resourceMap.allAssociatedReferenceGrants.Has(utils.NamespacedName(refGrant).String()) {
			resourceMap.allAssociatedReferenceGrants.Insert(utils.NamespacedName(refGrant).String())
			resourceTree.ReferenceGrants = append(resourceTree.ReferenceGrants, refGrant)
			p.reconciler.log.Info("added ReferenceGrant to resource map", "namespace", refGrant.Namespace, "name", refGrant.Name)
		}
	}
	return nil
}

// findReferenceGrant finds a ReferenceGrant that allows the specified cross-namespace reference
func (p *ReferenceProcessor) findReferenceGrant(ctx context.Context, from, to ObjectKindNamespacedName) (*gwapiv1b1.ReferenceGrant, error) {
	refGrantList := new(gwapiv1b1.ReferenceGrantList)
	opts := &client.ListOptions{FieldSelector: fields.OneTermEqualSelector(targetRefGrantRouteIndex, to.kind)}
	if err := p.reconciler.client.List(ctx, refGrantList, opts); err != nil {
		return nil, fmt.Errorf("failed to list ReferenceGrants: %w", err)
	}

	refGrants := refGrantList.Items
	if p.reconciler.namespaceLabel != nil {
		var rgs []gwapiv1b1.ReferenceGrant
		for _, refGrant := range refGrants {
			if ok, err := p.reconciler.checkObjectNamespaceLabels(&refGrant); err != nil {
				p.reconciler.log.Error(err, "failed to check namespace labels for ReferenceGrant %s in namespace %s: %w", refGrant.GetName(), refGrant.GetNamespace())
				continue
			} else if !ok {
				continue
			}
			rgs = append(rgs, refGrant)
		}
		refGrants = rgs
	}

	for _, refGrant := range refGrants {
		if p.matchesReferenceGrant(&refGrant, from, to) {
			return &refGrant, nil
		}
	}

	// No ReferenceGrant found.
	return nil, nil
}

// matchesReferenceGrant checks if a ReferenceGrant matches the given from and to references
func (p *ReferenceProcessor) matchesReferenceGrant(refGrant *gwapiv1b1.ReferenceGrant, from, to ObjectKindNamespacedName) bool {
	if refGrant.Namespace != to.namespace {
		return false
	}

	// Check if the 'from' reference is allowed
	var fromAllowed bool
	for _, refGrantFrom := range refGrant.Spec.From {
		if string(refGrantFrom.Kind) == from.kind && string(refGrantFrom.Namespace) == from.namespace {
			fromAllowed = true
			break
		}
	}

	if !fromAllowed {
		return false
	}

	// Check if the 'to' reference is allowed
	var toAllowed bool
	for _, refGrantTo := range refGrant.Spec.To {
		if string(refGrantTo.Kind) == to.kind && (refGrantTo.Name == nil || *refGrantTo.Name == "" || string(*refGrantTo.Name) == to.name) {
			toAllowed = true
			break
		}
	}

	return toAllowed
}
