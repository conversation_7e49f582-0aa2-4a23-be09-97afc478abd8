// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"context"

	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
)

// PolicyProcessor defines the interface for processing different types of policies
type PolicyProcessor interface {
	Process(ctx context.Context, resourceTree *resource.Resources, resourceMap *resourceMappings) error
}

// GatewayProcessor defines the interface for processing Gateway resources
type GatewayProcessor interface {
	ProcessGateways(ctx context.Context, managedGC *gwapiv1.GatewayClass, resourceMap *resourceMappings, resourceTree *resource.Resources) error
}

// RouteProcessor defines the interface for processing Route resources
type RouteProcessor interface {
	ProcessHTTPRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error
	ProcessGRPCRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error
	ProcessTLSRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error
	ProcessTCPRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error
	ProcessUDPRoutes(ctx context.Context, gatewayNamespacedName string, resourceMap *resourceMappings, resourceTree *resource.Resources) error
}

// ReferenceProcessor defines the interface for processing cross-namespace references
type ReferenceProcessor interface {
	ProcessBackendRefs(ctx context.Context, gwcResource *resource.Resources, resourceMappings *resourceMappings)
	ProcessBackendRef(ctx context.Context, resourceMap *resourceMappings, resourceTree *resource.Resources, ownerKind, ownerNS, ownerName string, backendRef gwapiv1.BackendObjectReference) error
}
